const hre = require("hardhat");

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("测试账号:", deployer.address);

    // 合约地址
    const routerAddress = "******************************************";
    const tokenAAddress = "******************************************";
    const tokenBAddress = "******************************************";

    console.log("=== 测试基本合约调用 ===");

    try {
        // 测试代币合约
        const tokenA = await hre.ethers.getContractAt("MyToken", tokenAAddress);
        const balanceA = await tokenA.balanceOf(deployer.address);
        console.log(`✅ TokenA余额读取成功: ${hre.ethers.formatUnits(balanceA, 18)}`);

        const tokenB = await hre.ethers.getContractAt("MyToken", tokenBAddress);
        const balanceB = await tokenB.balanceOf(deployer.address);
        console.log(`✅ TokenB余额读取成功: ${hre.ethers.formatUnits(balanceB, 18)}`);

        // 测试Router合约
        const router = await hre.ethers.getContractAt("UniswapV2Router02", routerAddress);
        const factory = await router.factory();
        console.log(`✅ Router.factory()调用成功: ${factory}`);

        const weth = await router.WETH();
        console.log(`✅ Router.WETH()调用成功: ${weth}`);

        // 尝试一个简单的授权
        console.log("\n=== 测试授权 ===");
        const amount = hre.ethers.parseUnits("1", 18);
        
        const approveTx = await tokenA.approve(routerAddress, amount);
        await approveTx.wait();
        console.log("✅ TokenA授权成功");

        const allowance = await tokenA.allowance(deployer.address, routerAddress);
        console.log(`✅ 授权额度确认: ${hre.ethers.formatUnits(allowance, 18)}`);

        // 尝试最小金额的addLiquidity
        console.log("\n=== 测试小金额添加流动性 ===");
        const smallAmount = hre.ethers.parseUnits("1", 18);
        const deadline = Math.floor(Date.now() / 1000) + 30 * 60;

        // 先授权TokenB
        const approveTxB = await tokenB.approve(routerAddress, smallAmount);
        await approveTxB.wait();
        console.log("✅ TokenB授权成功");

        try {
            const addLiquidityTx = await router.addLiquidity(
                tokenAAddress,
                tokenBAddress,
                smallAmount,
                smallAmount,
                0,
                0,
                deployer.address,
                deadline,
                {
                    gasLimit: 300000  // 较小的gas limit
                }
            );

            console.log(`交易发送成功，等待确认...`);
            const receipt = await addLiquidityTx.wait();
            
            if (receipt.status === 1) {
                console.log("🎉 小金额添加流动性成功！");
                console.log(`Gas使用: ${receipt.gasUsed.toString()}`);
            } else {
                console.log("❌ 交易失败");
                console.log(`Gas使用: ${receipt.gasUsed.toString()}`);
            }
        } catch (error) {
            console.log(`❌ 添加流动性失败: ${error.message}`);
        }

    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        console.log(error);
    }
}

main().catch(console.error); 
const hre = require("hardhat");

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("Deploying contracts with the account:", deployer.address);

    // 我们将要铸造的总量 (100万个代币，带18位小数)
    const initialSupply = hre.ethers.parseUnits("1000000", 18);

    // 1. 部署 TokenA
    const tokenA = await hre.ethers.deployContract("MyToken", ["Token A", "TKA"]);
    await tokenA.waitForDeployment();
    console.log(`Token A (TKA) deployed to: ${tokenA.target}`);
    // 为部署者铸造初始供应量
    await tokenA.mint(deployer.address, initialSupply);
    console.log(`Minted ${hre.ethers.formatUnits(initialSupply, 18)} TKA to ${deployer.address}`);

    console.log("\n----------------------------------------------------\n");

    // 2. 部署 TokenB
    const tokenB = await hre.ethers.deployContract("MyToken", ["Token B", "TKB"]);
    await tokenB.waitForDeployment();
    console.log(`Token B (TKB) deployed to: ${tokenB.target}`);
    // 为部署者铸造初始供应量
    await tokenB.mint(deployer.address, initialSupply);
    console.log(`Minted ${hre.ethers.formatUnits(initialSupply, 18)} TKB to ${deployer.address}`);


    console.log("\n--- TOKEN DEPLOYMENT COMPLETE ---");
    console.log(`TOKEN_A_ADDRESS=${tokenA.target}`);
    console.log(`TOKEN_B_ADDRESS=${tokenB.target}`);


}

main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
});


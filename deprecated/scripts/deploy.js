const hre = require("hardhat");

async function main() {
  const [deployer] = await hre.ethers.getSigners();
  console.log("部署账号:", deployer.address);

  // 1. 部署 WETH9
  const weth = await hre.ethers.deployContract("WETH9");
  await weth.waitForDeployment();
  console.log("✅ WETH:", weth.target);

  // 2. 部署 Factory
  const factory = await hre.ethers.deployContract("UniswapV2Factory", [deployer.address]);
  await factory.waitForDeployment();
  console.log("✅ Factory:", factory.target);

  // 3. 部署 Router
  const router = await hre.ethers.deployContract("UniswapV2Router02", [
    factory.target,
    weth.target,
  ]);
  await router.waitForDeployment();
  console.log("✅ Router:", router.target);

  // 4. TokenA
  const tokenA = await hre.ethers.deployContract("MyToken", ["TokenA", "TKA"]);
  await tokenA.waitForDeployment();
  console.log("✅ TokenA:", tokenA.target);

  // 5. TokenB
  const tokenB = await hre.ethers.deployContract("MyToken", ["TokenB", "TKB"]);
  await tokenB.waitForDeployment();
  console.log("✅ TokenB:", tokenB.target);

  // 6. 创建 Pair
  const tx = await factory.createPair(tokenA.target, tokenB.target);
  await tx.wait();
  const pairAddress = await factory.getPair(tokenA.target, tokenB.target);
  console.log("✅ Pair:", pairAddress);

  // 7. 地址清单输出（适合拷贝到 Go）
  console.log(`
📝 地址清单:
router  = "${router.target}"
factory = "${factory.target}"
weth    = "${weth.target}"
tokenA  = "${tokenA.target}"
tokenB  = "${tokenB.target}"
pair    = "${pairAddress}"
`);
}

main().catch((err) => {
  console.error(err);
  process.exit(1);
});

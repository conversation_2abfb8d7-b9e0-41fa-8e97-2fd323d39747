const hre = require("hardhat");

  async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("Deploying contracts with the account:", deployer.address);

    // Hardhat 会自动从 node_modules/@uniswap/... 中找到并编译这些合约
    // 然后我们就可以按名字直接部署

    const factory = await hre.ethers.deployContract("UniswapV2Factory", [deployer.address]);
    await factory.waitForDeployment();
    console.log(`UniswapV2Factory deployed to: ${factory.target}`);

    const weth = await hre.ethers.deployContract("WETH9");
    await weth.waitForDeployment();
    console.log(`WETH9 deployed to: ${weth.target}`);

    const router = await hre.ethers.deployContract("UniswapV2Router02", [factory.target, weth.target]);
    await router.waitForDeployment();
    console.log(`UniswapV2Router02 deployed to: ${router.target}`);
    
    console.log("\n--- DEPLOYMENT COMPLETE ---");
    console.log(`FACTORY_ADDRESS=${factory.target}`);
    console.log(`WETH_ADDRESS=${weth.target}`);
    console.log(`ROUTER_ADDRESS=${router.target}`);

  }

  main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
  });

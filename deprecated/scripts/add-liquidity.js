const hre = require("hardhat");

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("添加流动性账号:", deployer.address);

    // 合约地址
    const routerAddress = "******************************************";
    const tokenAAddress = "******************************************";
    const tokenBAddress = "******************************************";

    // 连接到合约
    const router = await hre.ethers.getContractAt("UniswapV2Router02", routerAddress);
    const tokenA = await hre.ethers.getContractAt("MyToken", tokenAAddress);
    const tokenB = await hre.ethers.getContractAt("MyToken", tokenBAddress);

    // 要添加的流动性数量（100 代币）
    const liquidityAmount = hre.ethers.parseUnits("100", 18);

    console.log("检查代币余额...");
    const balanceA = await tokenA.balanceOf(deployer.address);
    const balanceB = await tokenB.balanceOf(deployer.address);
    console.log(`TokenA 余额: ${hre.ethers.formatUnits(balanceA, 18)}`);
    console.log(`TokenB 余额: ${hre.ethers.formatUnits(balanceB, 18)}`);

    console.log("授权代币给 Router...");
    
    // 授权 TokenA
    const approveA = await tokenA.approve(routerAddress, liquidityAmount);
    await approveA.wait();
    console.log("✅ TokenA 授权完成");

    // 授权 TokenB
    const approveB = await tokenB.approve(routerAddress, liquidityAmount);
    await approveB.wait();
    console.log("✅ TokenB 授权完成");

    console.log("检查授权...");
    const allowanceA = await tokenA.allowance(deployer.address, routerAddress);
    const allowanceB = await tokenB.allowance(deployer.address, routerAddress);
    console.log(`TokenA 授权: ${hre.ethers.formatUnits(allowanceA, 18)}`);
    console.log(`TokenB 授权: ${hre.ethers.formatUnits(allowanceB, 18)}`);

    console.log("添加流动性...");
    
    // 设置截止时间（15分钟后）
    const deadline = Math.floor(Date.now() / 1000) + 15 * 60;

    try {
        const addLiquidityTx = await router.addLiquidity(
            tokenAAddress,
            tokenBAddress,
            liquidityAmount,
            liquidityAmount,
            0, // amountAMin
            0, // amountBMin
            deployer.address,
            deadline,
            {
                gasLimit: 500000 // 明确设置 gas limit
            }
        );

        console.log("等待交易确认...");
        const receipt = await addLiquidityTx.wait();
        
        console.log(`✅ 添加流动性成功！`);
        console.log(`交易哈希: ${receipt.transactionHash}`);
        console.log(`Gas 使用: ${receipt.gasUsed.toString()}`);
        console.log(`交易状态: ${receipt.status === 1 ? '成功' : '失败'}`);

    } catch (error) {
        console.log(`❌ 添加流动性失败: ${error.message}`);
        
        // 尝试获取更详细的错误信息
        if (error.transaction) {
            console.log("交易数据:", error.transaction);
        }
    }
}

main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
}); 
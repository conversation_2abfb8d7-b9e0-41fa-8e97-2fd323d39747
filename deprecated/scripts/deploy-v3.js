const hre = require("hardhat");

async function main() {
  const [deployer] = await hre.ethers.getSigners();
  console.log("部署账号:", deployer.address);

  // 1. 部署 WETH9 (V3也需要)
  const weth = await hre.ethers.deployContract("WETH9");
  await weth.waitForDeployment();
  console.log("✅ WETH:", weth.target);

  // 2. 部署 UniswapV3Factory
  try {
    const factory = await hre.ethers.deployContract("UniswapV3Factory");
    await factory.waitForDeployment();
    console.log("✅ UniswapV3Factory:", factory.target);

    // 3. 部署 SwapRouter
    const swapRouter = await hre.ethers.deployContract("SwapRouter", [
      factory.target,
      weth.target
    ]);
    await swapRouter.waitForDeployment();
    console.log("✅ SwapRouter:", swapRouter.target);

    // 4. 部署 NonfungiblePositionManager
    const positionManager = await hre.ethers.deployContract("NonfungiblePositionManager", [
      factory.target,
      weth.target,
      "******************************************" // tokenDescriptor (可以设为0地址)
    ]);
    await positionManager.waitForDeployment();
    console.log("✅ NonfungiblePositionManager:", positionManager.target);

    // 5. 部署代币 (重用之前的)
    const tokenA = await hre.ethers.deployContract("MyToken", ["TokenA", "TKA"]);
    await tokenA.waitForDeployment();
    console.log("✅ TokenA:", tokenA.target);

    const tokenB = await hre.ethers.deployContract("MyToken", ["TokenB", "TKB"]);
    await tokenB.waitForDeployment();
    console.log("✅ TokenB:", tokenB.target);

    // 6. 铸造代币
    const initialSupply = hre.ethers.parseUnits("1000000", 18);
    await tokenA.mint(deployer.address, initialSupply);
    await tokenB.mint(deployer.address, initialSupply);
    console.log("✅ 代币铸造完成");

    // 7. 创建流动性池 (在V3中需要指定fee tier)
    // 常见的fee tiers: 500 (0.05%), 3000 (0.3%), 10000 (1%)
    const feeTier = 3000; // 0.3%
    const createPoolTx = await factory.createPool(
      tokenA.target,
      tokenB.target,
      feeTier
    );
    await createPoolTx.wait();
    console.log("✅ 流动性池创建完成");

    // 获取池地址
    const poolAddress = await factory.getPool(tokenA.target, tokenB.target, feeTier);
    console.log("✅ Pool地址:", poolAddress);

    console.log(`
📝 Uniswap V3 地址清单:
factory             = "${factory.target}"
swapRouter          = "${swapRouter.target}"
positionManager     = "${positionManager.target}"
weth                = "${weth.target}"
tokenA              = "${tokenA.target}"
tokenB              = "${tokenB.target}"
pool                = "${poolAddress}"
feeTier             = ${feeTier}
`);

  } catch (error) {
    console.log("❌ 部署失败:", error.message);
    
    // 如果V3合约不可用，回退到简化版本
    console.log("\n尝试简化部署...");
    
    // 部署代币
    const tokenA = await hre.ethers.deployContract("MyToken", ["TokenA", "TKA"]);
    await tokenA.waitForDeployment();
    console.log("✅ TokenA:", tokenA.target);

    const tokenB = await hre.ethers.deployContract("MyToken", ["TokenB", "TKB"]);
    await tokenB.waitForDeployment();
    console.log("✅ TokenB:", tokenB.target);

    // 铸造代币
    const initialSupply = hre.ethers.parseUnits("1000000", 18);
    await tokenA.mint(deployer.address, initialSupply);
    await tokenB.mint(deployer.address, initialSupply);
    console.log("✅ 代币铸造完成");

    console.log(`
📝 简化版地址清单:
weth                = "${weth.target}"
tokenA              = "${tokenA.target}"
tokenB              = "${tokenB.target}"
`);
  }
}

main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
}); 
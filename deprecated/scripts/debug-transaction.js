const hre = require("hardhat");

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("调试账号:", deployer.address);

    // 合约地址
    const routerAddress = "******************************************";
    const tokenAAddress = "******************************************";
    const tokenBAddress = "******************************************";

    // 连接到合约
    const router = await hre.ethers.getContractAt("UniswapV2Router02", routerAddress);
    const tokenA = await hre.ethers.getContractAt("MyToken", tokenAAddress);
    const tokenB = await hre.ethers.getContractAt("MyToken", tokenBAddress);

    // 要添加的流动性数量
    const liquidityAmount = hre.ethers.parseUnits("100", 18);
    const deadline = Math.floor(Date.now() / 1000) + 30 * 60; // 30分钟后

    console.log("检查前置条件...");
    
    // 检查余额
    const balanceA = await tokenA.balanceOf(deployer.address);
    const balanceB = await tokenB.balanceOf(deployer.address);
    console.log(`TokenA余额: ${hre.ethers.formatUnits(balanceA, 18)}`);
    console.log(`TokenB余额: ${hre.ethers.formatUnits(balanceB, 18)}`);

    // 检查授权
    const allowanceA = await tokenA.allowance(deployer.address, routerAddress);
    const allowanceB = await tokenB.allowance(deployer.address, routerAddress);
    console.log(`TokenA授权: ${hre.ethers.formatUnits(allowanceA, 18)}`);
    console.log(`TokenB授权: ${hre.ethers.formatUnits(allowanceB, 18)}`);

    // 如果授权不足，先授权
    if (allowanceA < liquidityAmount) {
        console.log("授权TokenA...");
        const approveA = await tokenA.approve(routerAddress, liquidityAmount);
        await approveA.wait();
        console.log("✅ TokenA授权完成");
    }

    if (allowanceB < liquidityAmount) {
        console.log("授权TokenB...");
        const approveB = await tokenB.approve(routerAddress, liquidityAmount);
        await approveB.wait();
        console.log("✅ TokenB授权完成");
    }

    console.log("\n=== 尝试添加流动性（使用static call测试）===");
    
    try {
        // 使用 staticCall 来模拟交易而不实际执行
        const result = await router.addLiquidity.staticCall(
            tokenAAddress,
            tokenBAddress,
            liquidityAmount,
            liquidityAmount,
            0, // amountAMin
            0, // amountBMin
            deployer.address,
            deadline
        );
        
        console.log("✅ staticCall成功！");
        console.log(`预期返回：amountA=${result[0]}, amountB=${result[1]}, liquidity=${result[2]}`);
        
        // 如果staticCall成功，尝试实际执行
        console.log("\n=== 执行实际交易 ===");
        const tx = await router.addLiquidity(
            tokenAAddress,
            tokenBAddress,
            liquidityAmount,
            liquidityAmount,
            0, // amountAMin
            0, // amountBMin
            deployer.address,
            deadline,
            {
                gasLimit: 800000
            }
        );
        
        console.log(`交易哈希: ${tx.hash}`);
        console.log("等待交易确认...");
        
        const receipt = await tx.wait();
        console.log(`交易状态: ${receipt.status === 1 ? "成功" : "失败"}`);
        console.log(`Gas使用: ${receipt.gasUsed.toString()}`);
        console.log(`Gas价格: ${receipt.gasPrice.toString()}`);
        
        if (receipt.status === 1) {
            console.log("🎉 添加流动性成功！");
        } else {
            console.log("❌ 交易被回滚");
        }
        
    } catch (error) {
        console.log(`❌ 添加流动性失败: ${error.message}`);
        
        // 尝试获取更具体的错误信息
        if (error.data) {
            console.log("错误数据:", error.data);
        }
        
        // 检查是否是特定的Uniswap错误
        if (error.message.includes("INSUFFICIENT_LIQUIDITY")) {
            console.log("🔍 可能原因：流动性不足");
        } else if (error.message.includes("EXPIRED")) {
            console.log("🔍 可能原因：交易过期");
        } else if (error.message.includes("TRANSFER_FAILED")) {
            console.log("🔍 可能原因：代币转账失败，检查授权");
        } else if (error.message.includes("IDENTICAL_ADDRESSES")) {
            console.log("🔍 可能原因：代币地址相同");
        }
        
        console.log("\n完整错误对象：");
        console.log(error);
    }
}

main().catch(console.error); 
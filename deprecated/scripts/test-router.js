const hre = require("hardhat");

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("测试账号:", deployer.address);

    // 合约地址
    const routerAddress = "******************************************";
    const factoryAddress = "******************************************";
    const tokenAAddress = "******************************************";
    const tokenBAddress = "******************************************";

    try {
        // 连接到Router合约
        const router = await hre.ethers.getContractAt("UniswapV2Router02", routerAddress);
        console.log("✅ Router合约连接成功");

        // 检查Router的factory地址
        const routerFactory = await router.factory();
        console.log(`Router中的Factory地址: ${routerFactory}`);
        console.log(`实际Factory地址: ${factoryAddress}`);
        console.log(`Factory地址匹配: ${routerFactory === factoryAddress}`);

        // 检查Router的WETH地址
        const routerWETH = await router.WETH();
        console.log(`Router中的WETH地址: ${routerWETH}`);

        // 连接到Factory合约
        const factory = await hre.ethers.getContractAt("UniswapV2Factory", factoryAddress);
        console.log("✅ Factory合约连接成功");

        // 检查Pair地址
        const pairAddress = await factory.getPair(tokenAAddress, tokenBAddress);
        console.log(`Pair地址: ${pairAddress}`);
        console.log(`Pair是否存在: ${pairAddress !== "******************************************"}`);

        if (pairAddress !== "******************************************") {
            // 连接到Pair合约检查储备量
            const pair = await hre.ethers.getContractAt("UniswapV2Pair", pairAddress);
            const reserves = await pair.getReserves();
            console.log(`储备量0: ${reserves[0].toString()}`);
            console.log(`储备量1: ${reserves[1].toString()}`);
        }

        // 测试Router的基本功能
        console.log("\n=== 测试Router功能 ===");
        
        // 尝试调用getAmountsOut来测试是否能正常访问
        const testAmount = hre.ethers.parseUnits("1", 18);
        const path = [tokenAAddress, tokenBAddress];
        
        try {
            const amounts = await router.getAmountsOut(testAmount, path);
            console.log(`✅ getAmountsOut调用成功: ${amounts[0]} -> ${amounts[1]}`);
        } catch (error) {
            console.log(`❌ getAmountsOut调用失败: ${error.message}`);
        }

        // 检查代码是否存在
        const routerCode = await hre.ethers.provider.getCode(routerAddress);
        console.log(`Router合约代码长度: ${routerCode.length} 字符`);
        console.log(`Router合约是否存在: ${routerCode !== "0x"}`);

    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
        console.log(error);
    }
}

main().catch(console.error); 
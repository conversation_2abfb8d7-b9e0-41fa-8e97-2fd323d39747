# 废弃文件目录

这个目录包含了在项目开发过程中被废弃的文件和代码。

## 废弃原因

最初项目尝试使用Uniswap V2架构，但遇到了兼容性问题，导致添加流动性功能无法正常工作。经过多次调试和尝试后，最终决定采用自定义的SimpleAMM合约方案。

## 废弃文件说明

### contracts/
- 包含了Uniswap V2和V3相关的合约文件（已在之前清理时删除）

### scripts/
- `deploy.js` - 原始的合约部署脚本
- `deploy-tokens.js` - 代币部署脚本
- 其他与Uniswap相关的部署和测试脚本（已在之前清理时删除）

### ignition/
- Hardhat Ignition模块，原本用于合约部署管理

## 当前活跃文件

项目现在使用以下核心文件：
- `contracts/SimpleAMM.sol` - 自定义AMM合约
- `contracts/MyToken.sol` - ERC20代币合约
- `scripts/deploy-simple-amm.js` - SimpleAMM部署脚本
- `scripts/mint-tokens.js` - 代币铸造脚本
- `scripts/simple-test.js` - 功能测试脚本
- Go文件：`main.go`, `shared_config.go`, `add_liquidity.go`, `swap.go`, `check_balances.go`

## 注意

这些废弃文件保留作为参考，如果需要可以随时删除。 
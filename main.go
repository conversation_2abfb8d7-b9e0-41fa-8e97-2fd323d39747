package main

import (
	"fmt"
	"my-eth-app/goscripts"
	"os"
)

func main() {
	if len(os.Args) < 2 {
		fmt.Println("用法:")
		fmt.Println("  go run *.go check    - 检查代币余额")
		fmt.Println("  go run *.go add      - 添加流动性到SimpleAMM") 
		fmt.Println("  go run *.go swap     - 执行代币交换")
		return
	}

	switch os.Args[1] {
	case "check":
		goscripts.CheckBalances()
	case "add":
		goscripts.AddLiquidity()
	case "swap":
		goscripts.SwapTokens()
	default:
		fmt.Println("未知命令:", os.Args[1])
	}
} 
require("@nomicfoundation/hardhat-toolbox");


/** @type import('hardhat/config').HardhatUserConfig */
module.exports = {
  solidity: {
    compilers: [
      { version: "0.8.20" }, // For our custom tokens
      { version: "0.7.6", // For Uniswap V3
        settings: {
          optimizer: {
            enabled: true,
            runs: 800
          }
        }
      },
      { version: "0.6.6", 
        settings: {
          optimizer: {
            enabled: true,
            runs: 200
          }
        }
      }, // For Uniswap V2 Router and WETH (keeping for reference)
      { version: "0.5.16" , 
        settings: {
          optimizer: {
            enabled: true,
            runs: 200
          }
        }
      } // For Uniswap V2 Factory and Pair (keeping for reference)
    ]
  },
  networks: {
    // 添加这个 'geth_dev' 网络配置
    geth_dev: {
      url: "http://127.0.0.1:8545", // 指向我们 geth 节点的 RPC 地址
      // 我们需要告诉 hardhat 使用哪个账户来部署合约。
      // 就是我们之前 Go 脚本里那个硬编码的、已经充值过的账户。
      // `--dev` 模式的默认私钥，对应地址 ******************************************
      accounts: ["0xb71c71a67e1177ad4e901695e1b4b9ee17ae16c6668d313eac2f96dbcda3f291"],
      chainId: 1337
    }
  }
};

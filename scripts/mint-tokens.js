const hre = require("hardhat");

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("铸造账号:", deployer.address);

    // 代币地址（从最新部署获取）
    const tokenAAddress = "******************************************";
    const tokenBAddress = "******************************************";

    // 连接到已部署的代币合约
    const tokenA = await hre.ethers.getContractAt("MyToken", tokenAAddress);
    const tokenB = await hre.ethers.getContractAt("MyToken", tokenBAddress);

    // 铸造金额（1,000,000 代币，18位小数）
    const mintAmount = hre.ethers.parseUnits("1000000", 18);

    console.log("开始铸造代币...");

    // 铸造 TokenA
    const txA = await tokenA.mint(deployer.address, mintAmount);
    await txA.wait();
    console.log(`✅ TokenA 铸造成功: ${hre.ethers.formatUnits(mintAmount, 18)} 代币`);

    // 铸造 TokenB  
    const txB = await tokenB.mint(deployer.address, mintAmount);
    await txB.wait();
    console.log(`✅ TokenB 铸造成功: ${hre.ethers.formatUnits(mintAmount, 18)} 代币`);

    console.log("铸造完成！");
}

main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
}); 
const hre = require("hardhat");

async function main() {
    const [deployer] = await hre.ethers.getSigners();
    console.log("部署账号:", deployer.address);

    // 1. 部署代币
    const tokenA = await hre.ethers.deployContract("MyToken", ["TokenA", "TKA"]);
    await tokenA.waitForDeployment();
    console.log("✅ TokenA:", tokenA.target);

    const tokenB = await hre.ethers.deployContract("MyToken", ["TokenB", "TKB"]);
    await tokenB.waitForDeployment();
    console.log("✅ TokenB:", tokenB.target);

    // 2. 部署简化AMM
    const amm = await hre.ethers.deployContract("SimpleAMM", [
        tokenA.target,
        tokenB.target,
        "Simple AMM LP Token",
        "AMM-LP"
    ]);
    await amm.waitForDeployment();
    console.log("✅ SimpleAMM:", amm.target);

    // 3. 铸造代币
    const initialSupply = hre.ethers.parseUnits("1000000", 18);
    await tokenA.mint(deployer.address, initialSupply);
    await tokenB.mint(deployer.address, initialSupply);
    console.log("✅ 代币铸造完成");

    console.log(`
📝 简化AMM地址清单:
tokenA    = "${tokenA.target}"
tokenB    = "${tokenB.target}"
amm       = "${amm.target}"
`);

    // 4. 测试添加流动性
    console.log("\n=== 测试添加流动性 ===");
    const liquidityAmount = hre.ethers.parseUnits("1000", 18); // 1000 代币

    // 授权AMM合约
    console.log("授权代币给AMM...");
    await tokenA.approve(amm.target, liquidityAmount);
    await tokenB.approve(amm.target, liquidityAmount);
    console.log("✅ 授权完成");

    // 添加流动性
    const addLiquidityTx = await amm.addLiquidity(
        liquidityAmount,
        liquidityAmount,
        0, // minAmountA
        0  // minAmountB
    );
    await addLiquidityTx.wait();
    console.log("✅ 流动性添加成功");

    // 检查储备量
    const reserves = await amm.getReserves();
    console.log(`储备量A: ${hre.ethers.formatUnits(reserves[0], 18)}`);
    console.log(`储备量B: ${hre.ethers.formatUnits(reserves[1], 18)}`);

    // 检查LP代币余额
    const lpBalance = await amm.balanceOf(deployer.address);
    console.log(`LP代币余额: ${hre.ethers.formatUnits(lpBalance, 18)}`);

    console.log("\n🎉 简化AMM部署和测试完成！");
}

main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
}); 
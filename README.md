# 以太坊去中心化交易所 (DEX) 项目

这是一个基于以太坊的去中心化交易所项目，使用自定义的SimpleAMM合约实现代币交换和流动性管理功能。

## 项目结构

### 核心文件
- `contracts/SimpleAMM.sol` - 自定义AMM合约，实现x*y=k恒定乘积算法
- `contracts/MyToken.sol` - ERC20代币合约模板
- `shared_config.go` - Go应用的共享配置
- `main.go` - Go应用主入口
- `add_liquidity.go` - 添加流动性功能
- `swap.go` - 代币交换功能  
- `check_balances.go` - 余额查询功能

### 部署和测试脚本
- `scripts/deploy-simple-amm.js` - 部署SimpleAMM和代币合约
- `scripts/mint-tokens.js` - 铸造代币到指定地址
- `scripts/simple-test.js` - 完整的功能测试脚本

### 废弃文件
- `deprecated/` - 包含开发过程中废弃的文件（Uniswap相关代码）

## 功能特性

- ✅ 代币部署和铸造
- ✅ 流动性添加/移除
- ✅ 代币交换（A→B, B→A）
- ✅ 0.3%交易手续费
- ✅ Go语言区块链交互
- ✅ 完整的错误处理和状态检查

## 核心功能详解

### 1. SimpleAMM 智能合约功能

#### 流动性管理
- `addLiquidity(uint256 amountA, uint256 amountB)` - 添加流动性
- `removeLiquidity(uint256 liquidity)` - 移除流动性
- `getReserves()` - 获取当前储备量

#### 代币交换
- `swapAtoB(uint256 amountIn, uint256 amountOutMin)` - TokenA换TokenB
- `swapBtoA(uint256 amountIn, uint256 amountOutMin)` - TokenB换TokenA
- `getAmountOutAtoB(uint256 amountIn)` - 计算A→B预期输出
- `getAmountOutBtoA(uint256 amountIn)` - 计算B→A预期输出

#### 手续费机制
- 每笔交易收取0.3%手续费
- 手续费自动添加到流动性池中

### 2. Go 应用程序功能

#### 配置管理 (`shared_config.go`)
```go
// 网络配置
NodeURL = "http://localhost:8545"
PrivateKeyHex = "your_private_key"

// 合约地址
TokenAAddress = common.HexToAddress("0x...")
TokenBAddress = common.HexToAddress("0x...")
AMMAddress = common.HexToAddress("0x...")
```

#### 主程序 (`main.go`)
```go
// 命令行接口
go run . check    // 检查余额
go run . add      // 添加流动性
go run . swap     // 执行交换
```

#### 余额查询 (`check_balances.go`)
- 查询TokenA/TokenB余额
- 查询AMM池储备量
- 显示流动性代币余额

#### 流动性管理 (`add_liquidity.go`)
- 检查代币余额和授权
- 自动处理代币授权
- 添加流动性到AMM池
- 交易状态监控

#### 代币交换 (`swap.go`)
- 计算预期输出金额
- 处理滑点保护
- 执行代币交换
- 实时状态反馈

## 使用方法

### 1. 环境准备
```shell
# 安装Node.js依赖
npm install

# 安装Go依赖
go mod tidy

# 确保已安装Hardhat
npm install -g hardhat
```

### 2. 启动本地以太坊网络
```shell
npx hardhat node
```
这将启动一个本地以太坊网络，并提供10个测试账户，每个账户有10000 ETH。

### 3. 部署合约系统
```shell
# 部署TokenA、TokenB和SimpleAMM合约
npx hardhat run scripts/deploy-simple-amm.js --network localhost
```
部署成功后会显示合约地址，需要更新`shared_config.go`中的地址。

### 4. 铸造测试代币
```shell
# 向指定地址铸造代币（默认1,000,000个）
npx hardhat run scripts/mint-tokens.js --network localhost
```

### 5. 配置Go应用
编辑`shared_config.go`，更新以下配置：
- 合约地址（从部署输出中获取）
- 私钥（使用Hardhat提供的测试账户私钥）

### 6. 运行Go应用

#### 检查余额
```shell
go run . check
```
输出示例：
```
=== 余额检查 ===
TokenA 余额: 1000000000000000000000000
TokenB 余额: 1000000000000000000000000
AMM池 TokenA 储备: 0
AMM池 TokenB 储备: 0
```

#### 添加流动性
```shell
go run . add
```
功能：
- 检查当前余额和授权状态
- 自动处理代币授权（如需要）
- 添加100个TokenA和100个TokenB到流动性池
- 显示交易哈希和确认状态

#### 执行代币交换
```shell
go run . swap
```
功能：
- 用1个TokenA换取TokenB
- 显示预期输出金额
- 处理授权和滑点保护
- 监控交易状态

### 7. 运行完整测试
```shell
npx hardhat run scripts/simple-test.js --network localhost
```
这个脚本会：
1. 部署所有合约
2. 铸造代币
3. 测试添加流动性
4. 测试代币交换
5. 验证数学计算的正确性

## 技术实现细节

### AMM算法
使用恒定乘积公式：`x * y = k`
- x, y 分别为两种代币的储备量
- k 为恒定值
- 交换时保持乘积不变（扣除手续费后）

### 手续费计算
```solidity
uint256 amountInWithFee = amountIn * 997; // 0.3% 手续费
uint256 numerator = amountInWithFee * reserveOut;
uint256 denominator = reserveIn * 1000 + amountInWithFee;
amountOut = numerator / denominator;
```

### 交易安全
- 滑点保护：设置最小输出金额
- 授权检查：确保有足够的代币授权
- 余额验证：交易前检查余额充足性
- 状态监控：实时跟踪交易状态

## 技术栈

- **智能合约**: Solidity ^0.8.20, OpenZeppelin
- **开发框架**: Hardhat
- **后端**: Go + go-ethereum
- **区块链**: 以太坊 (本地测试网络)

## 合约地址 (示例)

- TokenA: `******************************************`
- TokenB: `******************************************`
- SimpleAMM: `******************************************`

*注意：实际部署时地址会有所不同*

## 故障排除

### 常见问题

1. **交易失败 (status: 0)**
   - 检查Gas限制是否足够
   - 确认代币授权是否充足
   - 验证合约地址是否正确

2. **余额不足**
   - 运行铸造脚本增加代币
   - 检查账户ETH余额用于支付Gas

3. **授权问题**
   - 程序会自动处理授权
   - 如果失败，手动检查allowance

4. **网络连接**
   - 确保Hardhat节点正在运行
   - 检查NodeURL配置是否正确

### 调试技巧
- 使用`simple-test.js`进行端到端测试
- 检查Hardhat节点的控制台输出
- 使用`check`命令验证当前状态



# 快速使用指南

## 一键启动

### 1. 启动环境
```bash
# 终端1：启动区块链网络
npx hardhat node

# 终端2：部署合约
npx hardhat run scripts/deploy-simple-amm.js --network localhost
npx hardhat run scripts/mint-tokens.js --network localhost
```

### 2. 更新配置
复制部署输出的合约地址到 `shared_config.go`：
```go
TokenAAddress = common.HexToAddress("0x新的TokenA地址")
TokenBAddress = common.HexToAddress("0x新的TokenB地址") 
AMMAddress = common.HexToAddress("0x新的AMM地址")
```

### 3. 运行程序
```bash
# 检查状态
go run . check

# 添加流动性
go run . add

# 执行交换
go run . swap
```

## 程序输出示例

### 检查余额
```
=== 余额检查 ===
TokenA 余额: 1000000000000000000000000 (1,000,000 tokens)
TokenB 余额: 1000000000000000000000000 (1,000,000 tokens)
AMM池 TokenA 储备: 0
AMM池 TokenB 储备: 0
流动性代币余额: 0
```

### 添加流动性
```
=== 添加流动性 ===
当前TokenA余额: 1000000000000000000000000
当前TokenB余额: 1000000000000000000000000
TokenA授权额度: 115792089237316195423570985008687907853269984665640564039457584007913129639935
TokenB授权额度: 115792089237316195423570985008687907853269984665640564039457584007913129639935
✅ 流动性交易已发送: 0x...
交易状态: 1 (1=成功, 0=失败)
Gas 使用: 128458
🎉 流动性添加成功！
```

### 代币交换
```
准备交换 1000000000000000000 TokenA
当前TokenA授权额度: 115792089237316195423570985008687907853269984665640564039457584007913129639935
检查交换预期输出...
预期可获得TokenB: 996006981039903216
执行交换...
✅ 交换交易已发送: 0x...
交易状态: 1 (1=成功, 0=失败)
Gas 使用: 87439
🎉 代币交换成功！
```

## 重要提示

1. **必须先启动Hardhat节点**才能运行其他命令
2. **每次重启节点**都需要重新部署合约并更新地址
3. **首次添加流动性**需要两种代币数量相等
4. **交换比例**基于当前池中的储备量自动计算
5. **手续费0.3%**会自动从交换金额中扣除

## 测试完整流程
```bash
# 运行完整的自动化测试
npx hardhat run scripts/simple-test.js --network localhost
``` 
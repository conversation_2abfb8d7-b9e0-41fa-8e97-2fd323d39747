// add_liquidity.go
package goscripts

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"log"
	"math/big"
	"strings"
	"time"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
)

func AddLiquidity() {
	client, err := ethclient.Dial(NodeURL)
	if err != nil {
		log.Fatalf("连接节点失败: %v", err)
	}

	privateKey, err := crypto.HexToECDSA(PrivateKeyHex)
	if err != nil {
		log.Fatalf("解析私钥失败: %v", err)
	}

	fromAddress := crypto.PubkeyToAddress(*privateKey.Public().(*ecdsa.PublicKey))
	fmt.Printf("账户地址: %s\n", fromAddress.Hex())

	parsedERC20ABI, err := abi.JSON(strings.NewReader(ERC20ABI))
	if err != nil {
		log.Fatalf("解析 ERC20 ABI 失败: %v", err)
	}

	parsedAMMABI, err := abi.JSON(strings.NewReader(SimpleAMMABI))
	if err != nil {
		log.Fatalf("解析 AMM ABI 失败: %v", err)
	}

	chainID, err := client.NetworkID(context.Background())
	if err != nil {
		log.Fatalf("获取链ID失败: %v", err)
	}

	fmt.Printf("链ID: %s\n", chainID.String())
	fmt.Printf("AMM地址: %s\n", AMMAddress.Hex())
	fmt.Printf("TokenA地址: %s\n", TokenAAddress.Hex())
	fmt.Printf("TokenB地址: %s\n", TokenBAddress.Hex())

	amount := new(big.Int)
	amount.SetString("100000000000000000000", 10) // 100 代币
	fmt.Printf("要添加的流动性数量: %s\n", amount.String())

	// 检查代币余额
	fmt.Println("\n=== 检查代币余额 ===")
	balanceA := getTokenBalanceForAdd(client, parsedERC20ABI, TokenAAddress, fromAddress)
	balanceB := getTokenBalanceForAdd(client, parsedERC20ABI, TokenBAddress, fromAddress)
	fmt.Printf("TokenA 余额: %s\n", balanceA.String())
	fmt.Printf("TokenB 余额: %s\n", balanceB.String())

	if balanceA.Cmp(amount) < 0 || balanceB.Cmp(amount) < 0 {
		log.Fatalf("代币余额不足")
	}

	// TokenA 授权给AMM
	fmt.Println("\n=== TokenA 授权 ===")
	txHashA := approveToken(client, privateKey, fromAddress, TokenAAddress, AMMAddress, amount, parsedERC20ABI, chainID)
	fmt.Printf("TokenA 授权交易: %s\n", txHashA.Hex())

	// TokenB 授权给AMM
	fmt.Println("\n=== TokenB 授权 ===")
	txHashB := approveToken(client, privateKey, fromAddress, TokenBAddress, AMMAddress, amount, parsedERC20ABI, chainID)
	fmt.Printf("TokenB 授权交易: %s\n", txHashB.Hex())

	fmt.Println("等待授权确认...")
	time.Sleep(15 * time.Second)

	// 检查授权
	fmt.Println("\n=== 检查授权 ===")
	allowanceA := getAllowance(client, parsedERC20ABI, TokenAAddress, fromAddress, AMMAddress)
	allowanceB := getAllowance(client, parsedERC20ABI, TokenBAddress, fromAddress, AMMAddress)
	fmt.Printf("TokenA 授权额度: %s\n", allowanceA.String())
	fmt.Printf("TokenB 授权额度: %s\n", allowanceB.String())

	if allowanceA.Cmp(amount) < 0 || allowanceB.Cmp(amount) < 0 {
		log.Fatalf("授权额度不足")
	}

	// 添加流动性到SimpleAMM
	fmt.Println("\n=== 添加流动性 ===")
	minAmountA := big.NewInt(0) // 对于第一次添加流动性，使用0作为最小金额
	minAmountB := big.NewInt(0)
	
	fmt.Printf("期望TokenA数量: %s\n", amount.String())
	fmt.Printf("期望TokenB数量: %s\n", amount.String())
	fmt.Printf("最小TokenA数量: %s\n", minAmountA.String())
	fmt.Printf("最小TokenB数量: %s\n", minAmountB.String())

	callData, err := parsedAMMABI.Pack("addLiquidity",
		amount, amount, minAmountA, minAmountB,
	)
	if err != nil {
		log.Fatalf("打包交易数据失败: %v", err)
	}

	nonce, err := client.PendingNonceAt(context.Background(), fromAddress)
	if err != nil {
		log.Fatalf("获取 nonce 失败: %v", err)
	}

	gasPrice, err := client.SuggestGasPrice(context.Background())
	if err != nil {
		log.Fatalf("获取 gas 价格失败: %v", err)
	}

	gasLimit := uint64(300000) // 对简化AMM使用更小的gas limit
	fmt.Printf("Gas limit: %d\n", gasLimit)
	fmt.Printf("Gas price: %s\n", gasPrice.String())
	fmt.Printf("Nonce: %d\n", nonce)

	tx := types.NewTransaction(nonce, AMMAddress, big.NewInt(0), gasLimit, gasPrice, callData)
	signedTx, err := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	if err != nil {
		log.Fatalf("签名交易失败: %v", err)
	}

	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		log.Fatalf("发送交易失败: %v", err)
	}

	fmt.Printf("✅ 添加流动性交易发送成功: %s\n", signedTx.Hash().Hex())

	// 等待交易确认
	fmt.Println("等待交易确认...")
	time.Sleep(25 * time.Second)

	receipt, err := client.TransactionReceipt(context.Background(), signedTx.Hash())
	if err != nil {
		fmt.Printf("获取交易回执失败: %v\n", err)
		// 尝试通过其他方式确认交易状态
		fmt.Println("尝试通过区块链查询交易状态...")
		
		// 检查交易是否在链上
		_, isPending, err := client.TransactionByHash(context.Background(), signedTx.Hash())
		if err != nil {
			fmt.Printf("查询交易失败: %v\n", err)
		} else if !isPending {
			fmt.Println("✅ 交易已在区块链上确认")
		} else {
			fmt.Println("交易仍在待确认状态")
		}
		
		// 即使获取回执失败，也继续检查AMM状态
		fmt.Println("\n=== 检查AMM状态 ===")
		reservesCallData, _ := parsedAMMABI.Pack("getReserves")
		reservesMsg := ethereum.CallMsg{To: &AMMAddress, Data: reservesCallData}
		reservesRes, err := client.CallContract(context.Background(), reservesMsg, nil)
		if err == nil {
			reservesResults, _ := parsedAMMABI.Unpack("getReserves", reservesRes)
			reserve0 := reservesResults[0].(*big.Int)
			reserve1 := reservesResults[1].(*big.Int)
			fmt.Printf("AMM储备量A: %s\n", reserve0.String())
			fmt.Printf("AMM储备量B: %s\n", reserve1.String())
			
			// 如果储备量不为0，说明添加流动性成功了
			if reserve0.Cmp(big.NewInt(0)) > 0 && reserve1.Cmp(big.NewInt(0)) > 0 {
				fmt.Println("🎉 根据储备量判断，添加流动性成功！")
			}
		}
		
		// 检查LP代币余额
		lpBalance := getTokenBalanceForAdd(client, parsedAMMABI, AMMAddress, fromAddress)
		fmt.Printf("LP代币余额: %s\n", lpBalance.String())
		
		return  // 提前返回，避免后续处理
	} else {
		fmt.Printf("交易状态: %d (1=成功, 0=失败)\n", receipt.Status)
		fmt.Printf("Gas 使用: %d\n", receipt.GasUsed)
		
		if receipt.Status == 1 {
			fmt.Println("🎉 添加流动性成功！")
			
			// 检查AMM储备量
			fmt.Println("\n=== 检查AMM状态 ===")
			reservesCallData, _ := parsedAMMABI.Pack("getReserves")
			reservesMsg := ethereum.CallMsg{To: &AMMAddress, Data: reservesCallData}
			reservesRes, err := client.CallContract(context.Background(), reservesMsg, nil)
			if err == nil {
				reservesResults, _ := parsedAMMABI.Unpack("getReserves", reservesRes)
				reserve0 := reservesResults[0].(*big.Int)
				reserve1 := reservesResults[1].(*big.Int)
				fmt.Printf("AMM储备量A: %s\n", reserve0.String())
				fmt.Printf("AMM储备量B: %s\n", reserve1.String())
			}
			
			// 检查LP代币余额
			lpBalance := getTokenBalanceForAdd(client, parsedAMMABI, AMMAddress, fromAddress)
			fmt.Printf("LP代币余额: %s\n", lpBalance.String())
			
		} else {
			fmt.Println("❌ 添加流动性失败")
		}
	}
}

func approveToken(client *ethclient.Client, privateKey *ecdsa.PrivateKey, from, token, spender common.Address, amount *big.Int, abi abi.ABI, chainID *big.Int) common.Hash {
	data, _ := abi.Pack("approve", spender, amount)
	nonce, _ := client.PendingNonceAt(context.Background(), from)
	gasPrice, _ := client.SuggestGasPrice(context.Background())
	tx := types.NewTransaction(nonce, token, big.NewInt(0), 100000, gasPrice, data)
	signedTx, _ := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	_ = client.SendTransaction(context.Background(), signedTx)
	return signedTx.Hash()
}

func getTokenBalanceForAdd(client *ethclient.Client, parsedABI abi.ABI, tokenAddr, account common.Address) *big.Int {
	data, _ := parsedABI.Pack("balanceOf", account)
	callMsg := ethereum.CallMsg{To: &tokenAddr, Data: data}
	output, _ := client.CallContract(context.Background(), callMsg, nil)
	results, _ := parsedABI.Unpack("balanceOf", output)
	return results[0].(*big.Int)
}

func getAllowance(client *ethclient.Client, parsedABI abi.ABI, tokenAddr, owner, spender common.Address) *big.Int {
	data, _ := parsedABI.Pack("allowance", owner, spender)
	callMsg := ethereum.CallMsg{To: &tokenAddr, Data: data}
	output, _ := client.CallContract(context.Background(), callMsg, nil)
	results, _ := parsedABI.Unpack("allowance", output)
	return results[0].(*big.Int)
} 
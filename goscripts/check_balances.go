package goscripts

import (
	"context"
	"fmt"
	"log"
	"math/big"
	"strings"

	ethereum "github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
)

func CheckBalances() {
	client, err := ethclient.Dial(NodeURL)
	if err != nil {
		log.Fatalf("无法连接到 Geth 节点: %v", err)
	}

	// 计算查询账户地址
	privateKey, err := crypto.HexToECDSA(PrivateKeyHex)
	if err != nil {
		log.Fatal(err)
	}
	account := crypto.PubkeyToAddress(privateKey.PublicKey)

	// 解析 ERC20 ABI
	parsedABI, err := abi.JSON(strings.NewReader(ERC20ABI))
	if err != nil {
		log.Fatal(err)
	}

	// 查询 TokenA 余额
	balanceA := getTokenBalance(client, parsedABI, TokenAAddress, account)

	// 查询 TokenB 余额
	balanceB := getTokenBalance(client, parsedABI, TokenBAddress, account)

	fmt.Printf("账户 %s 的余额:\n", account.Hex())
	fmt.Printf("  TokenA(%s): %s\n", TokenAAddress.Hex(), balanceA.String())
	fmt.Printf("  TokenB(%s): %s\n", TokenBAddress.Hex(), balanceB.String())
}

// getTokenBalance 使用 balanceOf 调用查询指定账户的 ERC20 余额
func getTokenBalance(client *ethclient.Client, parsedABI abi.ABI, tokenAddr, account common.Address) *big.Int {
	// 打包调用数据
	data, err := parsedABI.Pack("balanceOf", account)
	if err != nil {
		log.Fatal(err)
	}

	// 组装调用消息
	callMsg := ethereum.CallMsg{
		To:   &tokenAddr,
		Data: data,
	}

	// 执行 eth_call
	ctx := context.Background()
	output, err := client.CallContract(ctx, callMsg, nil)
	if err != nil {
		log.Fatal(err)
	}

	// 解析返回数据
	results, err := parsedABI.Unpack("balanceOf", output)
	if err != nil {
		log.Fatal(err)
	}

	if len(results) == 0 {
		log.Fatal("balanceOf 返回空结果")
	}

	balance, ok := results[0].(*big.Int)
	if !ok {
		log.Fatal("balanceOf 返回值类型断言失败")
	}
	return balance
} 
// swap.go
package goscripts

import (
	"context"
	"crypto/ecdsa"
	"fmt"
	"log"
	"math/big"
	"strings"
	"time"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/accounts/abi"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
)

func SwapTokens() {
	client, _ := ethclient.Dial(NodeURL)
	privateKey, _ := crypto.HexToECDSA(PrivateKeyHex)
	fromAddress := crypto.PubkeyToAddress(*privateKey.Public().(*ecdsa.PublicKey))
	chainID, _ := client.NetworkID(context.Background())

	parsedAMMABI, _ := abi.JSON(strings.NewReader(SimpleAMMABI))
	parsedERC20ABI, _ := abi.JSON(strings.NewReader(ERC20ABI))

	amountIn := new(big.Int)
	amountIn.SetString("1000000000000000000", 10) // 1 TokenA
	amountOutMin := big.NewInt(0)

	fmt.Printf("准备交换 %s TokenA\n", amountIn.String())

	// 检查授权
	callData, _ := parsedERC20ABI.Pack("allowance", fromAddress, AMMAddress)
	msg := ethereum.CallMsg{To: &TokenAAddress, Data: callData}
	res, _ := client.CallContract(context.Background(), msg, nil)
	currentAllowance := new(big.Int).SetBytes(res)
	fmt.Printf("当前TokenA授权额度: %s\n", currentAllowance.String())

	// 如果授权不足，先授权
	if currentAllowance.Cmp(amountIn) < 0 {
		fmt.Println("授权不足，正在授权...")
		approveData, _ := parsedERC20ABI.Pack("approve", AMMAddress, amountIn)
		nonce, _ := client.PendingNonceAt(context.Background(), fromAddress)
		gasPrice, _ := client.SuggestGasPrice(context.Background())
		approveTx := types.NewTransaction(nonce, TokenAAddress, big.NewInt(0), 100000, gasPrice, approveData)
		signedApproveTx, _ := types.SignTx(approveTx, types.NewEIP155Signer(chainID), privateKey)
		_ = client.SendTransaction(context.Background(), signedApproveTx)
		fmt.Println("等待授权确认...")
		time.Sleep(10 * time.Second)
	}

	// 检查预期输出
	fmt.Println("检查交换预期输出...")
	getAmountCallData, _ := parsedAMMABI.Pack("getAmountOutAtoB", amountIn)
	getAmountMsg := ethereum.CallMsg{To: &AMMAddress, Data: getAmountCallData}
	amountRes, err := client.CallContract(context.Background(), getAmountMsg, nil)
	if err != nil {
		log.Fatalf("❌ 获取预期输出失败：%v", err)
	}

	results, _ := parsedAMMABI.Unpack("getAmountOutAtoB", amountRes)
	expectedOut := results[0].(*big.Int)
	fmt.Printf("预期可获得TokenB: %s\n", expectedOut.String())

	// 执行交换
	fmt.Println("执行交换...")
	swapCallData, _ := parsedAMMABI.Pack("swapAtoB", amountIn, amountOutMin)
	nonce, _ := client.PendingNonceAt(context.Background(), fromAddress)
	gasPrice, _ := client.SuggestGasPrice(context.Background())
	tx := types.NewTransaction(nonce, AMMAddress, big.NewInt(0), 200000, gasPrice, swapCallData)
	signedTx, _ := types.SignTx(tx, types.NewEIP155Signer(chainID), privateKey)
	err = client.SendTransaction(context.Background(), signedTx)
	if err != nil {
		log.Fatalf("发送交易失败: %v", err)
	}

	fmt.Printf("✅ 交换交易已发送: %s\n", signedTx.Hash().Hex())
	
	// 等待确认
	time.Sleep(15 * time.Second)
	receipt, err := client.TransactionReceipt(context.Background(), signedTx.Hash())
	if err == nil {
		fmt.Printf("交易状态: %d (1=成功, 0=失败)\n", receipt.Status)
		fmt.Printf("Gas 使用: %d\n", receipt.GasUsed)
		if receipt.Status == 1 {
			fmt.Println("🎉 代币交换成功！")
		}
	}
}

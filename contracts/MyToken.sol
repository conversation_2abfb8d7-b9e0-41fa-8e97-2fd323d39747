// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

// 这是一个标准的、可增发的 ERC20 代币
contract MyToken is ERC20, Ownable {
    constructor(
        string memory name,
        string memory symbol
    )
        ERC20(name, symbol)
        Ownable(msg.sender) // 将合约的"所有权"赋予部署者
    {}

    // 只有合约的拥有者(owner)才能调用这个函数来"铸造"新币
    function mint(address to, uint256 amount) public onlyOwner {
        _mint(to, amount);
    }
}

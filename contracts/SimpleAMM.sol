// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

// 简化的自动做市商 (AMM) 合约
// 实现基本的 x * y = k 常数乘积公式
contract SimpleAMM is ERC20, Ownable {
    IERC20 public tokenA;
    IERC20 public tokenB;

    uint256 public reserveA;
    uint256 public reserveB;

    event LiquidityAdded(
        address indexed provider,
        uint256 amountA,
        uint256 amountB,
        uint256 liquidity
    );
    event LiquidityRemoved(
        address indexed provider,
        uint256 amountA,
        uint256 amountB,
        uint256 liquidity
    );
    event Swap(
        address indexed trader,
        address tokenIn,
        uint256 amountIn,
        address tokenOut,
        uint256 amountOut
    );

    constructor(
        address _tokenA,
        address _tokenB,
        string memory _name,
        string memory _symbol
    ) ERC20(_name, _symbol) Ownable(msg.sender) {
        tokenA = IERC20(_tokenA);
        tokenB = IERC20(_tokenB);
    }

    // 添加流动性
    function addLiquidity(
        uint256 amountA,
        uint256 amountB,
        uint256 minAmountA,
        uint256 minAmountB
    ) external returns (uint256 liquidity) {
        require(amountA > 0 && amountB > 0, "Invalid amounts");

        // 转移代币到合约
        tokenA.transferFrom(msg.sender, address(this), amountA);
        tokenB.transferFrom(msg.sender, address(this), amountB);

        // 计算流动性代币数量
        if (totalSupply() == 0) {
            // 第一次添加流动性
            liquidity = sqrt(amountA * amountB);
            require(liquidity > 0, "Insufficient liquidity");
        } else {
            // 后续添加流动性，按比例计算
            uint256 liquidityA = (amountA * totalSupply()) / reserveA;
            uint256 liquidityB = (amountB * totalSupply()) / reserveB;
            liquidity = liquidityA < liquidityB ? liquidityA : liquidityB;
        }

        require(liquidity > 0, "Insufficient liquidity minted");
        require(
            amountA >= minAmountA && amountB >= minAmountB,
            "Slippage exceeded"
        );

        // 铸造流动性代币
        _mint(msg.sender, liquidity);

        // 更新储备量
        reserveA += amountA;
        reserveB += amountB;

        emit LiquidityAdded(msg.sender, amountA, amountB, liquidity);
    }

    // 移除流动性
    function removeLiquidity(
        uint256 liquidity,
        uint256 minAmountA,
        uint256 minAmountB
    ) external returns (uint256 amountA, uint256 amountB) {
        require(liquidity > 0, "Invalid liquidity amount");
        require(
            balanceOf(msg.sender) >= liquidity,
            "Insufficient liquidity tokens"
        );

        // 计算可提取的代币数量
        amountA = (liquidity * reserveA) / totalSupply();
        amountB = (liquidity * reserveB) / totalSupply();

        require(
            amountA >= minAmountA && amountB >= minAmountB,
            "Slippage exceeded"
        );

        // 销毁流动性代币
        _burn(msg.sender, liquidity);

        // 更新储备量
        reserveA -= amountA;
        reserveB -= amountB;

        // 转移代币给用户
        tokenA.transfer(msg.sender, amountA);
        tokenB.transfer(msg.sender, amountB);

        emit LiquidityRemoved(msg.sender, amountA, amountB, liquidity);
    }

    // 交换代币A到代币B
    function swapAtoB(
        uint256 amountIn,
        uint256 minAmountOut
    ) external returns (uint256 amountOut) {
        require(amountIn > 0, "Invalid input amount");

        // 计算输出金额 (考虑0.3%手续费)
        uint256 amountInWithFee = amountIn * 997; // 99.7% (扣除0.3%手续费)
        amountOut =
            (amountInWithFee * reserveB) /
            (reserveA * 1000 + amountInWithFee);

        require(amountOut >= minAmountOut, "Slippage exceeded");
        require(amountOut < reserveB, "Insufficient liquidity");

        // 转移代币
        tokenA.transferFrom(msg.sender, address(this), amountIn);
        tokenB.transfer(msg.sender, amountOut);

        // 更新储备量
        reserveA += amountIn;
        reserveB -= amountOut;

        emit Swap(
            msg.sender,
            address(tokenA),
            amountIn,
            address(tokenB),
            amountOut
        );
    }

    // 交换代币B到代币A
    function swapBtoA(
        uint256 amountIn,
        uint256 minAmountOut
    ) external returns (uint256 amountOut) {
        require(amountIn > 0, "Invalid input amount");

        // 计算输出金额 (考虑0.3%手续费)
        uint256 amountInWithFee = amountIn * 997; // 99.7% (扣除0.3%手续费)
        amountOut =
            (amountInWithFee * reserveA) /
            (reserveB * 1000 + amountInWithFee);

        require(amountOut >= minAmountOut, "Slippage exceeded");
        require(amountOut < reserveA, "Insufficient liquidity");

        // 转移代币
        tokenB.transferFrom(msg.sender, address(this), amountIn);
        tokenA.transfer(msg.sender, amountOut);

        // 更新储备量
        reserveB += amountIn;
        reserveA -= amountOut;

        emit Swap(
            msg.sender,
            address(tokenB),
            amountIn,
            address(tokenA),
            amountOut
        );
    }

    // 获取交换预期输出 (A到B)
    function getAmountOutAtoB(
        uint256 amountIn
    ) external view returns (uint256) {
        require(amountIn > 0, "Invalid input amount");
        uint256 amountInWithFee = amountIn * 997;
        return
            (amountInWithFee * reserveB) / (reserveA * 1000 + amountInWithFee);
    }

    // 获取交换预期输出 (B到A)
    function getAmountOutBtoA(
        uint256 amountIn
    ) external view returns (uint256) {
        require(amountIn > 0, "Invalid input amount");
        uint256 amountInWithFee = amountIn * 997;
        return
            (amountInWithFee * reserveA) / (reserveB * 1000 + amountInWithFee);
    }

    // 获取储备量
    function getReserves() external view returns (uint256, uint256) {
        return (reserveA, reserveB);
    }

    // 平方根函数 (用于计算初始流动性)
    function sqrt(uint256 x) internal pure returns (uint256) {
        if (x == 0) return 0;
        uint256 z = (x + 1) / 2;
        uint256 y = x;
        while (z < y) {
            y = z;
            z = (x / z + z) / 2;
        }
        return y;
    }
}

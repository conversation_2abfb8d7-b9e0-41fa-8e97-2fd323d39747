# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a decentralized finance (DeFi) application built with React and `ethers.js`. The main functionalities seem to be swapping tokens and managing liquidity.

- **`src/App.js`**: The main application component that likely handles routing and wallet connection.
- **`src/components/Swap.js`**: Component for swapping between different cryptocurrencies.
- **`src/components/AddLiquidity.js`**: Component for adding liquidity to a liquidity pool.

## Common Commands

- **`npm start`**: Starts the development server.
- **`npm run build`**: Builds the app for production.
- **`npm test`**: Runs the tests.

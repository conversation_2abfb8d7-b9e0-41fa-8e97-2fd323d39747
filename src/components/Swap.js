import { useState, useEffect, useCallback } from 'react';
import { ethers } from 'ethers';

// TODO: 替换成你部署后得到的真实地址
const ROUTER_ADDRESS = "******************************************"; 
const TOKEN_A_ADDRESS = "******************************************";
const TOKEN_B_ADDRESS = "******************************************";

// Router ABI - 主要的swap函数
const ROUTER_ABI = [
    {
        "inputs": [
            {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
            {"internalType": "uint256", "name": "amountOutMin", "type": "uint256"},
            {"internalType": "address[]", "name": "path", "type": "address[]"},
            {"internalType": "address", "name": "to", "type": "address"},
            {"internalType": "uint256", "name": "deadline", "type": "uint256"}
        ],
        "name": "swapExactTokensForTokens",
        "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
            {"internalType": "uint256", "name": "reserveIn", "type": "uint256"},
            {"internalType": "uint256", "name": "reserveOut", "type": "uint256"}
        ],
        "name": "getAmountOut",
        "outputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}],
        "stateMutability": "pure",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
            {"internalType": "address[]", "name": "path", "type": "address[]"}
        ],
        "name": "getAmountsOut",
        "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "factory",
        "outputs": [{"internalType": "address", "name": "", "type": "address"}],
        "stateMutability": "view",
        "type": "function"
    }
];

// ERC20 ABI - 标准ERC20函数
const ERC20_ABI = [
    {
        "inputs": [{"internalType": "address", "name": "owner", "type": "address"}],
        "name": "balanceOf",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "spender", "type": "address"},
            {"internalType": "uint256", "name": "value", "type": "uint256"}
        ],
        "name": "approve",
        "outputs": [{"internalType": "bool", "name": "", "type": "bool"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "owner", "type": "address"},
            {"internalType": "address", "name": "spender", "type": "address"}
        ],
        "name": "allowance",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "decimals",
        "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}],
        "stateMutability": "view",
        "type": "function"
    }
]; 

function Swap() {
    const [provider, setProvider] = useState(null);
    const [signer, setSigner] = useState(null);
    const [tokenA, setTokenA] = useState(null);
    const [tokenB, setTokenB] = useState(null);
    const [router, setRouter] = useState(null);

    const [balanceA, setBalanceA] = useState("0");
    const [balanceB, setBalanceB] = useState("0");
    
    const [amountIn, setAmountIn] = useState("");
    const [amountOut, setAmountOut] = useState("");
    const [isLoading, setIsLoading] = useState(false);

    // 使用 useCallback 记忆 getAmountOut 函数
    const getAmountOut = useCallback(async (amountInValue) => {
        if (!router || !amountInValue || amountInValue === "0") {
            setAmountOut("");
            return;
        }

        try {
            console.log("=== getAmountOut 调试 ===");
            console.log("输入金额:", amountInValue);
            
            const amountInWei = ethers.parseUnits(amountInValue, 18);
            const path = [TOKEN_A_ADDRESS, TOKEN_B_ADDRESS];
            
            console.log("amountInWei:", amountInWei.toString());
            console.log("path:", path);

            // 检查 Factory 和 Pair
            const factoryAddress = await router.factory();
            console.log("Factory 地址:", factoryAddress);
            
            const factoryAbi = [
                "function getPair(address tokenA, address tokenB) view returns (address pair)"
            ];
            const factory = new ethers.Contract(factoryAddress, factoryAbi, router.runner);
            const pairAddress = await factory.getPair(TOKEN_A_ADDRESS, TOKEN_B_ADDRESS);
            console.log("Pair 地址:", pairAddress);

            if (pairAddress === "******************************************") {
                console.log("❌ Pair 不存在!");
                setAmountOut("需要先添加流动性");
                return;
            }

            // 检查储备
            const pairAbi = [
                "function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)",
                "function token0() view returns (address)",
                "function token1() view returns (address)"
            ];
            const pair = new ethers.Contract(pairAddress, pairAbi, router.runner);
            const reserves = await pair.getReserves();
            const token0 = await pair.token0();
            
            console.log("Token0:", token0);
            console.log("Token1:", TOKEN_B_ADDRESS);
            console.log("Reserve0:", ethers.formatUnits(reserves.reserve0, 18));
            console.log("Reserve1:", ethers.formatUnits(reserves.reserve1, 18));

            if (reserves.reserve0 === 0n || reserves.reserve1 === 0n) {
                console.log("❌ 储备为0!");
                setAmountOut("流动性不足");
                return;
            }

            // 尝试获取输出金额
            const amounts = await router.getAmountsOut(amountInWei, path);
            const amountOutValue = ethers.formatUnits(amounts[1], 18);
            console.log("✅ 预期输出:", amountOutValue);
            setAmountOut(amountOutValue);
            
        } catch (error) {
            console.error("getAmountOut 失败:", error);
            
            if (error.message.includes("INSUFFICIENT_LIQUIDITY")) {
                setAmountOut("流动性不足");
            } else if (error.message.includes("INVALID_PATH")) {
                setAmountOut("无效路径");
            } else {
                setAmountOut("计算失败: " + (error.reason || error.message));
            }
        }
    }, [router]); // router 是 getAmountOut 的依赖

    useEffect(() => {
        if (window.ethereum) {
            const newProvider = new ethers.BrowserProvider(window.ethereum);
            setProvider(newProvider);
        }
    }, []);

    // 当输入数量改变时，自动计算预期输出
    useEffect(() => {
        const timer = setTimeout(() => {
            if (amountIn && router) {
                getAmountOut(amountIn);
            }
        }, 500); // 防抖，500ms后执行

        return () => clearTimeout(timer);
    }, [amountIn, router, getAmountOut]);

    const connectAndLoadContracts = async () => {
        if (!provider) return;

        try {
            const newSigner = await provider.getSigner();
            setSigner(newSigner);

            const routerContract = new ethers.Contract(ROUTER_ADDRESS, ROUTER_ABI, newSigner);
            setRouter(routerContract);

            const tokenAContract = new ethers.Contract(TOKEN_A_ADDRESS, ERC20_ABI, newSigner);
            setTokenA(tokenAContract);
            
            const tokenBContract = new ethers.Contract(TOKEN_B_ADDRESS, ERC20_ABI, newSigner);
            setTokenB(tokenBContract);

            console.log("Contracts loaded");
            await updateBalances(newSigner.address, tokenAContract, tokenBContract);

        } catch (error) {
            console.error("Failed to connect or load contracts:", error);
        }
    };
    
    const updateBalances = async (address, tokenAContract, tokenBContract) => {
        try {
            const balA = await tokenAContract.balanceOf(address);
            setBalanceA(ethers.formatUnits(balA, 18));
            
            const balB = await tokenBContract.balanceOf(address);
            setBalanceB(ethers.formatUnits(balB, 18));
        } catch (error) {
            console.error("Failed to update balances:", error);
        }
    };

    const handleSwap = async () => {
        if (!router || !tokenA || !signer || !amountIn || amountIn === "0") {
            alert("请先连接钱包并输入有效的交换数量");
            return;
        }

        setIsLoading(true);
        try {
            const amountInWei = ethers.parseUnits(amountIn, 18);
            const path = [TOKEN_A_ADDRESS, TOKEN_B_ADDRESS];
            const deadline = Math.floor(Date.now() / 1000) + 60 * 20; // 20 minutes from now

            // 调试信息：检查基本参数
            console.log("=== 交换调试信息 ===");
            console.log("amountIn:", amountIn);
            console.log("amountInWei:", amountInWei.toString());
            console.log("path:", path);
            console.log("用户地址:", signer.address);

            // 检查用户余额
            const userBalance = await tokenA.balanceOf(signer.address);
            console.log("用户 Token A 余额:", ethers.formatUnits(userBalance, 18));
            
            if (userBalance < amountInWei) {
                alert(`余额不足！当前余额: ${ethers.formatUnits(userBalance, 18)}, 需要: ${amountIn}`);
                return;
            }

            // 检查 Factory 和 Pair 是否存在
            const factoryAddress = await router.factory();
            console.log("Factory 地址:", factoryAddress);
            
            const factoryAbi = [
                "function getPair(address tokenA, address tokenB) view returns (address pair)"
            ];
            const factory = new ethers.Contract(factoryAddress, factoryAbi, signer);
            const pairAddress = await factory.getPair(TOKEN_A_ADDRESS, TOKEN_B_ADDRESS);
            console.log("Pair 地址:", pairAddress);

            if (pairAddress === "******************************************") {
                alert("错误：Token A 和 Token B 之间没有流动性对！请先添加流动性。");
                return;
            }

            // 检查储备量
            const pairAbi = [
                "function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)",
                "function token0() view returns (address)",
                "function token1() view returns (address)"
            ];
            const pair = new ethers.Contract(pairAddress, pairAbi, signer);
            const reserves = await pair.getReserves();
            const token0 = await pair.token0();
            const token1 = await pair.token1();
            
            console.log("Token0 地址:", token0);
            console.log("Token1 地址:", token1);
            console.log("Reserve0:", ethers.formatUnits(reserves.reserve0, 18));
            console.log("Reserve1:", ethers.formatUnits(reserves.reserve1, 18));

            if (reserves.reserve0 === 0n || reserves.reserve1 === 0n) {
                alert("错误：流动性对存在但储备为0！请添加流动性。");
                return;
            }

            // 尝试获取预期输出
            let expectedAmounts;
            try {
                expectedAmounts = await router.getAmountsOut(amountInWei, path);
                console.log("预期输出:", ethers.formatUnits(expectedAmounts[1], 18));
            } catch (error) {
                console.error("getAmountsOut 失败:", error);
                alert("无法计算预期输出，可能是流动性不足");
                return;
            }

            const amountOutMinWei = ethers.parseUnits((parseFloat(ethers.formatUnits(expectedAmounts[1], 18)) * 0.95).toString(), 18);
            console.log("最小输出 (5% 滑点):", ethers.formatUnits(amountOutMinWei, 18));

            // 检查授权
            const allowance = await tokenA.allowance(signer.address, ROUTER_ADDRESS);
            console.log("当前授权:", ethers.formatUnits(allowance, 18));
            
            if (allowance < amountInWei) {
                console.log("需要授权...");
                const approveTx = await tokenA.approve(ROUTER_ADDRESS, amountInWei);
                console.log("授权交易hash:", approveTx.hash);
                await approveTx.wait();
                console.log("授权成功");
            }

            // 执行交换
            console.log("开始执行交换...");
            console.log("参数:");
            console.log("- amountIn:", amountInWei.toString());
            console.log("- amountOutMin:", amountOutMinWei.toString());
            console.log("- path:", path);
            console.log("- to:", signer.address);
            console.log("- deadline:", deadline);

            const swapTx = await router.swapExactTokensForTokens(
                amountInWei,
                amountOutMinWei,
                path,
                signer.address,
                deadline
            );
            
            await swapTx.wait();
            console.log("交换成功:", swapTx.hash);
            
            // 更新余额
            await updateBalances(signer.address, tokenA, tokenB);
            
            // 清空输入
            setAmountIn("");
            setAmountOut("");
            
            alert("交换成功!");

        } catch (error) {
            console.error("交换失败:", error);
            
            if (error.code === "CALL_EXCEPTION" || error.message.includes("execution reverted")) {
                alert(`交换失败: 合约执行被回滚。可能原因:\n1. 流动性不足\n2. 滑点设置过低\n3. 交易截止时间过期\n4. 授权问题\n\n请检查控制台日志获取详细信息。`);
            } else {
                alert(`交换失败: ${error.message || error.reason || "未知错误"}`);
            }
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div style={{ padding: '20px', maxWidth: '500px', margin: '0 auto' }}>
            <h2>代币交换</h2>
            <button 
                onClick={connectAndLoadContracts} 
                disabled={!!signer}
                style={{ 
                    padding: '10px 20px', 
                    backgroundColor: signer ? '#28a745' : '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: signer ? 'default' : 'pointer',
                    marginBottom: '20px'
                }}
            >
                {signer ? "✅ 合约已加载" : "🔗 连接钱包并加载合约"}
            </button>
            
            <div style={{ 
                padding: '15px', 
                backgroundColor: '#f8f9fa', 
                borderRadius: '5px',
                marginBottom: '20px'
            }}>
                <h4>当前余额:</h4>
                <p><strong>Token A:</strong> {parseFloat(balanceA).toFixed(6)}</p>
                <p><strong>Token B:</strong> {parseFloat(balanceB).toFixed(6)}</p>
            </div>

            <hr />

            <div>
                <h3>交换 Token A 为 Token B</h3>
                <div style={{ marginBottom: '10px' }}>
                    <label>输入 Token A 数量:</label>
                    <input
                        type="number"
                        placeholder="输入要交换的 Token A 数量"
                        value={amountIn}
                        onChange={(e) => setAmountIn(e.target.value)}
                        disabled={isLoading}
                        style={{ marginLeft: '10px', padding: '5px' }}
                    />
                </div>
                
                {amountOut && (
                    <div style={{ marginBottom: '10px', padding: '10px', backgroundColor: '#f0f0f0' }}>
                        <p><strong>预期获得 Token B:</strong> {parseFloat(amountOut).toFixed(6)}</p>
                        <p style={{ fontSize: '12px', color: '#666' }}>
                            (包含 5% 滑点保护)
                        </p>
                    </div>
                )}
                
                <button 
                    onClick={handleSwap} 
                    disabled={isLoading || !amountIn || amountIn === "0"}
                    style={{ 
                        padding: '10px 20px', 
                        backgroundColor: isLoading ? '#ccc' : '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '5px',
                        cursor: isLoading ? 'not-allowed' : 'pointer'
                    }}
                >
                    {isLoading ? "交换中..." : "执行交换"}
                </button>
            </div>
        </div>
    );
}

export default Swap;

import { useState, useEffect } from 'react';
import { ethers } from 'ethers';

// 确保这些地址与实际部署的地址一致 - 最新部署 2024
const ROUTER_ADDRESS = "******************************************"; // 最新部署的 Router
const TOKEN_A_ADDRESS = "******************************************"; // 最新部署的 Token A
const TOKEN_B_ADDRESS = "******************************************"; // 最新部署的 Token B



// 完整的 Uniswap V2 Router ABI
const ROUTER_ABI = [
    {"inputs":[{"internalType":"address","name":"_factory","type":"address"},{"internalType":"address","name":"_WETH","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},
    {"inputs":[],"name":"WETH","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},
    {"inputs":[{"internalType":"address","name":"tokenA","type":"address"},{"internalType":"address","name":"tokenB","type":"address"},{"internalType":"uint256","name":"amountADesired","type":"uint256"},{"internalType":"uint256","name":"amountBDesired","type":"uint256"},{"internalType":"uint256","name":"amountAMin","type":"uint256"},{"internalType":"uint256","name":"amountBMin","type":"uint256"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"deadline","type":"uint256"}],"name":"addLiquidity","outputs":[{"internalType":"uint256","name":"amountA","type":"uint256"},{"internalType":"uint256","name":"amountB","type":"uint256"},{"internalType":"uint256","name":"liquidity","type":"uint256"}],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[],"name":"factory","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},
    {"inputs":[{"internalType":"uint256","name":"amountOut","type":"uint256"},{"internalType":"uint256","name":"reserveIn","type":"uint256"},{"internalType":"uint256","name":"reserveOut","type":"uint256"}],"name":"getAmountIn","outputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"}],"stateMutability":"pure","type":"function"},
    {"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"uint256","name":"reserveIn","type":"uint256"},{"internalType":"uint256","name":"reserveOut","type":"uint256"}],"name":"getAmountOut","outputs":[{"internalType":"uint256","name":"amountOut","type":"uint256"}],"stateMutability":"pure","type":"function"},
    {"inputs":[{"internalType":"uint256","name":"amountOut","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"}],"name":"getAmountsIn","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"view","type":"function"},
    {"inputs":[{"internalType":"uint256","name":"amountIn","type":"uint256"},{"internalType":"address[]","name":"path","type":"address[]"}],"name":"getAmountsOut","outputs":[{"internalType":"uint256[]","name":"amounts","type":"uint256[]"}],"stateMutability":"view","type":"function"}
];

// 完整的 ERC20 ABI，包含我们的 MyToken 合约的所有方法
const ERC20_ABI = [
    {"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"}],"stateMutability":"nonpayable","type":"constructor"},
    {"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},
    {"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},
    {"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},
    {"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},
    {"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},
    {"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},
    {"inputs":[{"internalType":"address","name":"owner","type":"address"}],"name":"OwnableInvalidOwner","type":"error"},
    {"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"OwnableUnauthorizedAccount","type":"error"},
    {"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},
    {"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"previousOwner","type":"address"},{"indexed":true,"internalType":"address","name":"newOwner","type":"address"}],"name":"OwnershipTransferred","type":"event"},
    {"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},
    {"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},
    {"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},
    {"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},
    {"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},
    {"inputs":[],"name":"owner","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},
    {"inputs":[],"name":"renounceOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},
    {"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},
    {"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},
    {"inputs":[{"internalType":"address","name":"newOwner","type":"address"}],"name":"transferOwnership","outputs":[],"stateMutability":"nonpayable","type":"function"}
]; 

// FACTORY ABI - 仅包含 getPair 方法
const FACTORY_ABI = [
    {
        "inputs": [
            {"internalType": "address", "name": "tokenA", "type": "address"},
            {"internalType": "address", "name": "tokenB", "type": "address"}
        ],
        "name": "getPair",
        "outputs": [
            {"internalType": "address", "name": "pair", "type": "address"}
        ],
        "stateMutability": "view",
        "type": "function"
    }
];

// PAIR ABI - 仅包含 getReserves, token0, token1 方法
const PAIR_ABI = [
    {
        "inputs": [],
        "name": "getReserves",
        "outputs": [
            {"internalType": "uint112", "name": "_reserve0", "type": "uint112"},
            {"internalType": "uint112", "name": "_reserve1", "type": "uint112"},
            {"internalType": "uint32", "name": "_blockTimestampLast", "type": "uint32"}
        ],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "token0",
        "outputs": [
            {"internalType": "address", "name": "", "type": "address"}
        ],
        "stateMutability": "view",
        "type": "function"
    },
    {
        "inputs": [],
        "name": "token1",
        "outputs": [
            {"internalType": "address", "name": "", "type": "address"}
        ],
        "stateMutability": "view",
        "type": "function"
    }
];

function AddLiquidity() {
    const [provider, setProvider] = useState(null);
    const [signer, setSigner] = useState(null);
    const [router, setRouter] = useState(null);
    const [factoryContract, setFactoryContract] = useState(null);
    const [pairContract, setPairContract] = useState(null);
    const [tokenA, setTokenA] = useState(null);
    const [tokenB, setTokenB] = useState(null);
    const [balanceA, setBalanceA] = useState("0");
    const [balanceB, setBalanceB] = useState("0");
    const [amountA, setAmountA] = useState("");
    const [amountB, setAmountB] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [reserveA, setReserveA] = useState(0);
    const [reserveB, setReserveB] = useState(0);
    const [pairExists, setPairExists] = useState(false);
    const [token0Address, setToken0Address] = useState("");
    const [token1Address, setToken1Address] = useState("");
    const [isInitializing, setIsInitializing] = useState(false);
    const [currentRatio, setCurrentRatio] = useState("0");
    const [lastChangedInput, setLastChangedInput] = useState("");

    // 初始化 provider 和合约
    useEffect(() => {
        const initializeProvider = async () => {
            if (window.ethereum) {
                try {
                    const newProvider = new ethers.BrowserProvider(window.ethereum);
                    setProvider(newProvider);
                    
                    const newSigner = await newProvider.getSigner();
                    setSigner(newSigner);
                    
                    // 初始化合约
                    const routerContract = new ethers.Contract(ROUTER_ADDRESS, ROUTER_ABI, newSigner);
                    setRouter(routerContract);
                    
                    const tokenAContract = new ethers.Contract(TOKEN_A_ADDRESS, ERC20_ABI, newSigner);
                    const tokenBContract = new ethers.Contract(TOKEN_B_ADDRESS, ERC20_ABI, newSigner);
                    setTokenA(tokenAContract);
                    setTokenB(tokenBContract);
                    
                    // 获取工厂合约地址
                    const factoryAddress = await routerContract.factory();
                    const factory = new ethers.Contract(factoryAddress, FACTORY_ABI, newProvider);
                    setFactoryContract(factory);
                    
                } catch (error) {
                    console.error("初始化失败:", error);
                }
            }
        };
        
        initializeProvider();
    }, []);

    // 查询流动性池和储备
    useEffect(() => {
        const checkPairAndReserves = async () => {
            if (!factoryContract || !provider) return;
            
            try {
                setIsInitializing(true);
                
                // 查询交易对地址
                const pairAddress = await factoryContract.getPair(TOKEN_A_ADDRESS, TOKEN_B_ADDRESS);
                
                if (pairAddress === "******************************************") {
                    // 交易对不存在
                    setPairExists(false);
                    setReserveA(0);
                    setReserveB(0);
                    setCurrentRatio("0");
                    console.log("交易对不存在，这将是首次添加流动性");
                } else {
                    // 交易对存在，获取储备信息
                    setPairExists(true);
                    const pair = new ethers.Contract(pairAddress, PAIR_ABI, provider);
                    setPairContract(pair);
                    
                    // 获取token0和token1地址
                    const token0 = await pair.token0();
                    const token1 = await pair.token1();
                    setToken0Address(token0);
                    setToken1Address(token1);
                    
                    // 获取储备
                    const reserves = await pair.getReserves();
                    
                    // 确定哪个是reserveA，哪个是reserveB
                    let currentReserveA, currentReserveB;
                    if (token0.toLowerCase() === TOKEN_A_ADDRESS.toLowerCase()) {
                        currentReserveA = reserves._reserve0;
                        currentReserveB = reserves._reserve1;
                    } else {
                        currentReserveA = reserves._reserve1;
                        currentReserveB = reserves._reserve0;
                    }
                    
                    setReserveA(currentReserveA);
                    setReserveB(currentReserveB);
                    
                    // 计算当前比例 (TokenB/TokenA)
                    if (currentReserveA > 0) {
                        const ratio = Number(currentReserveB) / Number(currentReserveA);
                        setCurrentRatio(ratio.toFixed(6));
                    }
                    
                    console.log("找到交易对:", pairAddress);
                    console.log("储备 A:", ethers.formatUnits(currentReserveA, 18));
                    console.log("储备 B:", ethers.formatUnits(currentReserveB, 18));
                }
                
            } catch (error) {
                console.error("查询交易对失败:", error);
                setPairExists(false);
            } finally {
                setIsInitializing(false);
            }
        };
        
        checkPairAndReserves();
    }, [factoryContract, provider]);

    // 获取用户余额
    useEffect(() => {
        const getBalances = async () => {
            if (!tokenA || !tokenB || !signer) return;
            
            try {
                const address = await signer.getAddress();
                const balA = await tokenA.balanceOf(address);
                const balB = await tokenB.balanceOf(address);
                
                setBalanceA(ethers.formatUnits(balA, 18));
                setBalanceB(ethers.formatUnits(balB, 18));
            } catch (error) {
                console.error("获取余额失败:", error);
            }
        };
        
        getBalances();
    }, [tokenA, tokenB, signer]);

    // 根据输入自动计算对应数量
    const calculateAmountB = (inputAmountA) => {
        if (!pairExists || reserveA === 0 || reserveB === 0) {
            return "";
        }
        
        if (inputAmountA === "" || isNaN(inputAmountA)) {
            return "";
        }
        
        const amountAWei = ethers.parseUnits(inputAmountA, 18);
        const calculatedAmountB = (amountAWei * reserveB) / reserveA;
        return ethers.formatUnits(calculatedAmountB, 18);
    };

    const calculateAmountA = (inputAmountB) => {
        if (!pairExists || reserveA === 0 || reserveB === 0) {
            return "";
        }
        
        if (inputAmountB === "" || isNaN(inputAmountB)) {
            return "";
        }
        
        const amountBWei = ethers.parseUnits(inputAmountB, 18);
        const calculatedAmountA = (amountBWei * reserveA) / reserveB;
        return ethers.formatUnits(calculatedAmountA, 18);
    };

    // 处理Token A输入变化
    const handleAmountAChange = (value) => {
        setAmountA(value);
        setLastChangedInput("A");
        
        if (pairExists) {
            const calculatedB = calculateAmountB(value);
            setAmountB(calculatedB);
        }
    };

    // 处理Token B输入变化
    const handleAmountBChange = (value) => {
        setAmountB(value);
        setLastChangedInput("B");
        
        if (pairExists) {
            const calculatedA = calculateAmountA(value);
            setAmountA(calculatedA);
        }
    };

    // 添加流动性
    const addLiquidity = async () => {
        if (!router || !tokenA || !tokenB || !signer) {
            alert("请先连接钱包");
            return;
        }

        if (!amountA || !amountB) {
            alert("请输入数量");
            return;
        }

        try {
            setIsLoading(true);
            const address = await signer.getAddress();

                            console.log("=== 开始添加流动性 ===");
                console.log("用户地址:", address);
                console.log("Router地址:", ROUTER_ADDRESS);
                console.log("Token A地址:", TOKEN_A_ADDRESS);
                console.log("Token B地址:", TOKEN_B_ADDRESS);
                console.log("流动性池存在:", pairExists);
                console.log("储备A:", reserveA ? ethers.formatUnits(reserveA, 18) : "0");
                console.log("储备B:", reserveB ? ethers.formatUnits(reserveB, 18) : "0");

            const amountAWei = ethers.parseUnits(amountA, 18);
            const amountBWei = ethers.parseUnits(amountB, 18);

            console.log("数量 A (Wei):", amountAWei.toString());
            console.log("数量 B (Wei):", amountBWei.toString());

            // 检查余额
            const balanceAWei = await tokenA.balanceOf(address);
            const balanceBWei = await tokenB.balanceOf(address);
            console.log("Token A 余额:", ethers.formatUnits(balanceAWei, 18));
            console.log("Token B 余额:", ethers.formatUnits(balanceBWei, 18));

            if (balanceAWei < amountAWei) {
                alert(`Token A 余额不足！需要 ${amountA}，但只有 ${ethers.formatUnits(balanceAWei, 18)}`);
                return;
            }

            if (balanceBWei < amountBWei) {
                alert(`Token B 余额不足！需要 ${amountB}，但只有 ${ethers.formatUnits(balanceBWei, 18)}`);
                return;
            }

            // 检查当前授权
            const allowanceA = await tokenA.allowance(address, ROUTER_ADDRESS);
            const allowanceB = await tokenB.allowance(address, ROUTER_ADDRESS);
            
            console.log("当前 Token A 授权:", ethers.formatUnits(allowanceA, 18));
            console.log("当前 Token B 授权:", ethers.formatUnits(allowanceB, 18));

            // 授权足够的数量
            const approveAmount = ethers.parseUnits("1000000", 18); // 授权 100万个代币

            if (allowanceA < amountAWei) {
                console.log("授权 Token A...");
                const approveATx = await tokenA.approve(ROUTER_ADDRESS, approveAmount);
                console.log("Token A 授权交易:", approveATx.hash);
                await approveATx.wait();
                console.log("Token A 授权完成");
            }

            if (allowanceB < amountBWei) {
                console.log("授权 Token B...");
                const approveBTx = await tokenB.approve(ROUTER_ADDRESS, approveAmount);
                console.log("Token B 授权交易:", approveBTx.hash);
                await approveBTx.wait();
                console.log("Token B 授权完成");
            }

            // 设置最小数量（简化策略：总是设为0，像Go代码一样）
            let amountAMin = 0n;
            let amountBMin = 0n;
            
            console.log("简化策略：最小数量设为0（与成功的Go代码一致）");

            console.log("最小数量 A:", ethers.formatUnits(amountAMin, 18));
            console.log("最小数量 B:", ethers.formatUnits(amountBMin, 18));

            // 添加流动性
            const deadline = Math.floor(Date.now() / 1000) + 1200; // 20分钟后过期
            console.log("截止时间:", deadline);

            console.log("调用 addLiquidity...");
            
            // 检查网络ID
            const network = await provider.getNetwork();
            console.log("当前网络:", network.chainId, network.name);
            
            // 检查区块高度
            const blockNumber = await provider.getBlockNumber();
            console.log("当前区块:", blockNumber);
            
            // 尝试先估算gas
            console.log("估算gas...");
            try {
                const gasEstimate = await router.addLiquidity.estimateGas(
                    TOKEN_A_ADDRESS,
                    TOKEN_B_ADDRESS,
                    amountAWei,
                    amountBWei,
                    amountAMin,
                    amountBMin,
                    address,
                    deadline
                );
                console.log("Gas估算:", gasEstimate.toString());
            } catch (gasError) {
                console.error("Gas估算失败:", gasError);
                throw new Error("Gas估算失败: " + gasError.message);
            }
            
            const tx = await router.addLiquidity(
                TOKEN_A_ADDRESS,
                TOKEN_B_ADDRESS,
                amountAWei,
                amountBWei,
                amountAMin,
                amountBMin,
                address,
                deadline
            );

            console.log("交易已提交:", tx.hash);
            const receipt = await tx.wait();
            console.log("交易确认:", receipt);

            alert("流动性添加成功!");
            
            // 重置输入
            setAmountA("");
            setAmountB("");
            
            // 重新获取余额
            const newBalA = await tokenA.balanceOf(address);
            const newBalB = await tokenB.balanceOf(address);
            setBalanceA(ethers.formatUnits(newBalA, 18));
            setBalanceB(ethers.formatUnits(newBalB, 18));

            // 如果是首次添加流动性，重新查询交易对状态
            if (!pairExists) {
                window.location.reload(); // 简单的方式，重新加载页面来更新状态
            }

        } catch (error) {
            console.error("添加流动性失败:", error);
            
            // 更详细的错误信息
            let errorMessage = "添加流动性失败";
            if (error.message.includes("require(false)")) {
                errorMessage += "\n可能原因：\n1. 合约地址不正确\n2. Token授权失败\n3. 余额不足\n4. 滑点设置过小";
            } else if (error.message.includes("insufficient")) {
                errorMessage += ": 余额不足或授权不足";
            } else if (error.message.includes("deadline")) {
                errorMessage += ": 交易超时";
            } else {
                errorMessage += ": " + error.message;
            }
            
            alert(errorMessage);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div style={{ 
            maxWidth: '500px', 
            margin: '0 auto', 
            padding: '20px',
            fontFamily: 'Arial, sans-serif'
        }}>
            <h2 style={{ textAlign: 'center', marginBottom: '30px' }}>添加流动性</h2>
            
            {/* 流动性池状态显示 */}
            <div style={{
                background: '#f5f5f5',
                padding: '15px',
                borderRadius: '10px',
                marginBottom: '20px'
            }}>
                <h3 style={{ margin: '0 0 10px 0', fontSize: '16px' }}>池状态</h3>
                {isInitializing ? (
                    <p>正在查询流动性池...</p>
                ) : pairExists ? (
                    <div>
                        <p style={{ margin: '5px 0', color: '#28a745' }}>✅ 流动性池已存在</p>
                        <p style={{ margin: '5px 0', fontSize: '14px' }}>
                            储备 A: {ethers.formatUnits(reserveA, 18)} Token A
                        </p>
                        <p style={{ margin: '5px 0', fontSize: '14px' }}>
                            储备 B: {ethers.formatUnits(reserveB, 18)} Token B
                        </p>
                        <p style={{ margin: '5px 0', fontSize: '14px' }}>
                            当前比例: 1 Token A = {currentRatio} Token B
                        </p>
                    </div>
                ) : (
                    <div>
                        <p style={{ margin: '5px 0', color: '#ffc107' }}>⚠️ 流动性池不存在</p>
                        <p style={{ margin: '5px 0', fontSize: '14px' }}>
                            这将是首次添加流动性，你可以设定初始比例
                        </p>
                    </div>
                )}
            </div>

            {/* Token A 输入 */}
            <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    Token A 数量:
                </label>
                <input
                    type="number"
                    value={amountA}
                    onChange={(e) => handleAmountAChange(e.target.value)}
                    placeholder="输入 Token A 数量"
                    style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #ddd',
                        borderRadius: '8px',
                        fontSize: '16px',
                        boxSizing: 'border-box'
                    }}
                />
                <p style={{ 
                    margin: '5px 0', 
                    fontSize: '12px', 
                    color: '#666' 
                }}>
                    余额: {parseFloat(balanceA).toFixed(6)} Token A
                </p>
            </div>

            {/* Token B 输入 */}
            <div style={{ marginBottom: '20px' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold' }}>
                    Token B 数量:
                </label>
                <input
                    type="number"
                    value={amountB}
                    onChange={(e) => handleAmountBChange(e.target.value)}
                    placeholder="输入 Token B 数量"
                    style={{
                        width: '100%',
                        padding: '12px',
                        border: '2px solid #ddd',
                        borderRadius: '8px',
                        fontSize: '16px',
                        boxSizing: 'border-box'
                    }}
                />
                <p style={{ 
                    margin: '5px 0', 
                    fontSize: '12px', 
                    color: '#666' 
                }}>
                    余额: {parseFloat(balanceB).toFixed(6)} Token B
                </p>
            </div>

            {/* 比例提示 */}
            {pairExists && amountA && amountB && (
                <div style={{
                    background: '#e3f2fd',
                    padding: '10px',
                    borderRadius: '8px',
                    marginBottom: '20px',
                    fontSize: '14px'
                }}>
                    <p style={{ margin: '0' }}>
                        💡 数量已根据当前池比例自动调整
                    </p>
                    <p style={{ margin: '5px 0 0 0', color: '#666' }}>
                        当前输入比例: 1 Token A = {amountB && amountA ? (parseFloat(amountB) / parseFloat(amountA)).toFixed(6) : '0'} Token B
                    </p>
                </div>
            )}

            {/* 添加流动性按钮 */}
            <button
                onClick={addLiquidity}
                disabled={isLoading || !amountA || !amountB}
                style={{
                    width: '100%',
                    padding: '15px',
                    backgroundColor: isLoading || !amountA || !amountB ? '#ccc' : '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    cursor: isLoading || !amountA || !amountB ? 'not-allowed' : 'pointer',
                    transition: 'background-color 0.3s'
                }}
            >
                {isLoading ? '处理中...' : '添加流动性'}
            </button>

            {/* 刷新池状态按钮 */}
            <button
                onClick={() => window.location.reload()}
                style={{
                    width: '100%',
                    padding: '10px',
                    backgroundColor: '#28a745',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: 'bold',
                    cursor: 'pointer',
                    marginTop: '10px',
                    marginBottom: '20px'
                }}
            >
                🔄 刷新池状态
            </button>

            {/* 调试信息 */}
            <div style={{
                marginTop: '20px',
                padding: '15px',
                background: '#f0f8ff',
                borderRadius: '8px',
                fontSize: '12px',
                color: '#666'
            }}>
                <h4 style={{ margin: '0 0 10px 0', color: '#333' }}>系统信息:</h4>
                <p style={{ margin: '2px 0' }}>Router: {ROUTER_ADDRESS}</p>
                <p style={{ margin: '2px 0' }}>Token A: {TOKEN_A_ADDRESS}</p>
                <p style={{ margin: '2px 0' }}>Token B: {TOKEN_B_ADDRESS}</p>
                <p style={{ margin: '2px 0' }}>交易对存在: {pairExists ? '是' : '否'}</p>
                {pairExists && (
                    <>
                        <p style={{ margin: '2px 0' }}>Token0: {token0Address}</p>
                        <p style={{ margin: '2px 0' }}>Token1: {token1Address}</p>
                        <p style={{ margin: '2px 0' }}>储备A: {reserveA ? ethers.formatUnits(reserveA, 18) : '0'}</p>
                        <p style={{ margin: '2px 0' }}>储备B: {reserveB ? ethers.formatUnits(reserveB, 18) : '0'}</p>
                    </>
                )}
            </div>

            {/* 使用说明 */}
            <div style={{
                marginTop: '20px',
                padding: '15px',
                background: '#f8f9fa',
                borderRadius: '8px',
                fontSize: '14px',
                color: '#666'
            }}>
                <h4 style={{ margin: '0 0 10px 0' }}>使用说明:</h4>
                <ul style={{ margin: '0', paddingLeft: '20px' }}>
                    <li>如果流动性池已存在，输入任一代币数量将自动计算另一代币的对应数量</li>
                    <li>如果是首次添加流动性，你可以自由设定初始比例</li>
                    <li>系统会自动处理代币授权</li>
                    <li>添加流动性时允许滑点保护</li>
                    <li>请确保合约地址正确，检查浏览器控制台查看详细日志</li>
                </ul>
            </div>
        </div>
    );
}

export default AddLiquidity;

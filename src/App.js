import { useState } from 'react';
import { ethers } from 'ethers';
import Swap from './components/Swap'; // 导入 Swap 组件
import AddLiquidity from './components/AddLiquidity'; // 导入 AddLiquidity 组件

function App() {
  const [account, setAccount] = useState(null);

  async function connectWallet() {
    if (window.ethereum) {
      try {
        const provider = new ethers.BrowserProvider(window.ethereum);
        // 请求用户授权
        const accounts = await provider.send("eth_requestAccounts", []);
        setAccount(accounts[0]);
        console.log("Connected account:", accounts[0]);
      } catch (error) {
        console.error("User denied account access");
      }
    } else {
      alert("Please install MetaMask!");
    }
  }

  return (
    <div className="App">
      <header className="App-header">
        <h1>Uniswap Frontend</h1>
        {account ? (
          <div>
            <p>Connected: {account}</p>
            <hr />
            <AddLiquidity />
            <hr />
            <Swap />
          </div>
        ) : (
          <button onClick={connectWallet}>Connect Wallet</button>
        )}
      </header>
    </div>
  );
}

export default App;
